import type { Metadata } from 'next';
import LandingWildberries from "@/screens/microLandings/Wildberries";

export const metadata: Metadata = {
  title: 'Создание обложек для Wildberries с ИИ - Холст.ИИ',
  description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео для Wildberries',
  keywords: 'обложки маркетплейс,обложки вайлдберрис,Wildberries, OZON, ЯндексМаркет,  Amazon, товарные фото, 3D рендеры, баннеры для акций',
  openGraph: {
    title: 'Создание обложек для маркетплейсов с ИИ - Холст.ИИ',
    description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео для Wildberries',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Создание обложек для маркетплейсов с ИИ - Холст.ИИ',
    description: 'Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео для Wildberries',
  },
};

export default function WildberriesPage() {
  return <LandingWildberries />;
}
